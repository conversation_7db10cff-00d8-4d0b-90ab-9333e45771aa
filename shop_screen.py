import pygame
from background import Background
from shop import ShopCard, TabButton, ConfirmDialog, save_purchase
from ui_components import TextButton
import json
from utils import load_font, resource_path

def draw_shop_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    clock = pygame.time.Clock()
    scroll_y = 0

    # Load data
    with open(resource_path('data/shop_data.json')) as f:
        shop_data = json.load(f)
    parts_data = shop_data[0]
    cars_data = shop_data[1]['cars']
    with open(resource_path('data/profile.json')) as f:
        profile_data = json.load(f)

    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)

    # Create main tabs
    tab_width = 150
    tab_height = 50
    tab_y = 120
    cars_tab = TabButton('Samochody', s_width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
    parts_tab = TabButton('<PERSON><PERSON><PERSON><PERSON><PERSON>', s_width // 2 + 10, tab_y, tab_width, tab_height)

    # Create part category tabs
    part_categories = {
        "engine": "Silniki",
        "turbo": "Turbo",
        "intercooler": "Intercooler",
        "ecu": "ECU"
    }

    category_tab_width = 120
    category_tab_height = 40
    category_tabs = {}
    category_tab_y = 180

    # Calculate starting position for category tabs to center them
    total_category_width = len(part_categories) * category_tab_width + (len(part_categories) - 1) * 10
    category_start_x = (s_width - total_category_width) // 2

    for i, (category_key, category_name) in enumerate(part_categories.items()):
        x = category_start_x + i * (category_tab_width + 10)
        category_tabs[category_key] = TabButton(category_name, x, category_tab_y, category_tab_width, category_tab_height)

    # Set initial tab states
    current_tab = "cars"
    current_part_category = "engine"  # Default part category
    cars_tab.is_active = True
    category_tabs["engine"].is_active = True

    # Filter and sort options
    filter_compatible_only = False
    sort_options = ["name", "price_low", "price_high", "power_high", "power_low"]
    sort_names = {
        "name": "Nazwa",
        "price_low": "Cena ↑",
        "price_high": "Cena ↓",
        "power_high": "Moc ↓",
        "power_low": "Moc ↑"
    }
    current_sort = "name"

    # Create filter and sort buttons
    filter_button = TextButton('Tylko kompatybilne', s_width - 200, 180, font_size=20, width=180, height=30)
    sort_button = TextButton(f'Sortuj: {sort_names[current_sort]}', s_width - 200, 220, font_size=20, width=180, height=30)
    
    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 24)
    money_font = load_font("arial", 36)
    
    header = header_font.render("Sklep", True, (255, 255, 255))
    
    # Dialog state
    confirm_dialog = None
    selected_item = None
    selected_item_type = None
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        dt = clock.tick(60) / 1000.0
        
        # Handle events and scrolling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if confirm_dialog:
                    confirm_dialog = None
                else:
                    return
            if event.type == pygame.MOUSEWHEEL:
                scroll_y = max(0, scroll_y - event.y * 30)
        
        # Keyboard scrolling
        keys = pygame.key.get_pressed()
        if keys[pygame.K_UP]:
            scroll_y = max(0, scroll_y - 10)
        if keys[pygame.K_DOWN]:
            scroll_y += 10
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)

        # Update filter and sort buttons (only for parts tab)
        if current_tab == "parts":
            filter_button.update(mouse_pos, mouse_click)
            sort_button.update(mouse_pos, mouse_click)

            # Handle filter button click
            if filter_button.is_hovered and mouse_click[0] and not confirm_dialog:
                filter_compatible_only = not filter_compatible_only
                filter_text = "Tylko kompatybilne ✓" if filter_compatible_only else "Tylko kompatybilne"
                filter_button.label = filter_text
                filter_button.render()

            # Handle sort button click
            if sort_button.is_hovered and mouse_click[0] and not confirm_dialog:
                current_sort_index = sort_options.index(current_sort)
                current_sort = sort_options[(current_sort_index + 1) % len(sort_options)]
                sort_button.label = f'Sortuj: {sort_names[current_sort]}'
                sort_button.render()

        # Check back button
        if back_button.is_hovered and mouse_click[0] and not confirm_dialog:
            return
        
        # Handle dialogs
        if confirm_dialog:
            dialog_result = confirm_dialog.update(mouse_pos, mouse_click)
            if dialog_result == "confirm":
                try:
                    # save_purchase handles money deduction and validation
                    save_purchase(selected_item, selected_item_type)
                    # Reload profile data to get updated money amount
                    with open(resource_path('data/profile.json')) as f:
                        profile_data = json.load(f)
                except ValueError as e:
                    print(f"Purchase failed: {e}")
                except Exception as e:
                    print(f"Error during purchase: {e}")
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
            elif dialog_result == "cancel":
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
        else:
            # Update main tabs
            if cars_tab.update(mouse_pos, mouse_click):
                current_tab = "cars"
                cars_tab.is_active = True
                parts_tab.is_active = False
                # Deactivate all category tabs
                for cat_tab in category_tabs.values():
                    cat_tab.is_active = False
                scroll_y = 0  # Reset scroll when changing tabs

            if parts_tab.update(mouse_pos, mouse_click):
                current_tab = "parts"
                cars_tab.is_active = False
                parts_tab.is_active = True
                # Activate default category tab
                category_tabs[current_part_category].is_active = True
                scroll_y = 0  # Reset scroll when changing tabs

            # Update category tabs (only when parts tab is active)
            if current_tab == "parts":
                for category_key, cat_tab in category_tabs.items():
                    if cat_tab.update(mouse_pos, mouse_click):
                        current_part_category = category_key
                        # Deactivate all category tabs and activate selected one
                        for other_tab in category_tabs.values():
                            other_tab.is_active = False
                        cat_tab.is_active = True
                        scroll_y = 0  # Reset scroll when changing category
        
        # Get items for current tab
        if current_tab == "cars":
            items = cars_data
            item_type = "car"
        else:
            # Get parts for selected category only
            items = []
            if current_part_category in parts_data:
                for part in parts_data[current_part_category]:
                    part['category'] = current_part_category
                    items.append(part)

            # Apply filtering
            if filter_compatible_only and profile_data["inventory"]["owned_cars"]:
                from car_compatibility import car_compatibility
                filtered_items = []
                for part in items:
                    part_name = part.get('name', '')
                    part_category = part.get('category', '')

                    # Check if compatible with any owned car
                    is_compatible_with_any = False
                    for car_name in profile_data["inventory"]["owned_cars"]:
                        is_compatible, _ = car_compatibility.is_part_compatible(car_name, part_name, part_category)
                        if is_compatible:
                            is_compatible_with_any = True
                            break

                    if is_compatible_with_any:
                        filtered_items.append(part)
                items = filtered_items

            # Apply sorting
            if current_sort == "name":
                items.sort(key=lambda x: x.get('name', ''))
            elif current_sort == "price_low":
                items.sort(key=lambda x: x.get('value', 0))
            elif current_sort == "price_high":
                items.sort(key=lambda x: x.get('value', 0), reverse=True)
            elif current_sort == "power_high":
                items.sort(key=lambda x: x.get('horsepower', x.get('horsepower_boost_percentage', 0)), reverse=True)
            elif current_sort == "power_low":
                items.sort(key=lambda x: x.get('horsepower', x.get('horsepower_boost_percentage', 0)))

            item_type = "part"
        
        # Create cards layout
        cards_per_row = 4
        card_width = 250
        card_height = 300 if current_tab == "cars" else 280  # More height for parts with compatibility info
        card_spacing = 30
        start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
        start_y = 240 if current_tab == "parts" else 200  # More space for category tabs
        
        # Calculate max scroll
        total_rows = (len(items) + cards_per_row - 1) // cards_per_row
        content_height = total_rows * (card_height + card_spacing)
        max_scroll = max(0, content_height - (s_height - start_y))
        scroll_y = min(scroll_y, max_scroll)
        
        # Create and update shop cards
        shop_cards = []
        for i, item in enumerate(items):
            row = i // cards_per_row
            col = i % cards_per_row
            x = start_x + col * (card_width + card_spacing)
            y = start_y + row * (card_height + card_spacing) - scroll_y
            
            # Skip cards that are completely outside the visible area
            if y + card_height < start_y or y > s_height:
                continue
            
            # Check if item is owned and count
            is_owned = False
            owned_count = 0
            if item_type == "car":
                is_owned = item["name"] in profile_data["inventory"]["owned_cars"]
            else:
                # Count how many of this part we own
                owned_parts_in_category = profile_data["inventory"]["owned_parts"].get(item["category"], [])
                owned_count = owned_parts_in_category.count(item["name"])
                is_owned = owned_count > 0

            # Get owned cars for compatibility checking
            owned_cars = profile_data["inventory"]["owned_cars"]

            card = ShopCard(item, x, y, card_width, card_height, item_type, is_owned, owned_cars, owned_count)
            
            # Check if card was clicked
            if not confirm_dialog and card.update(mouse_pos, mouse_click):
                # For cars, only allow purchase if not owned. For parts, always allow purchase
                can_purchase = False
                if item_type == "car":
                    can_purchase = not is_owned and profile_data['money'] >= item['value']
                else:  # parts
                    can_purchase = profile_data['money'] >= item['value']

                if can_purchase:
                    selected_item = item
                    selected_item_type = item_type
                    # Show confirm dialog
                    confirm_dialog = ConfirmDialog(item['name'], item['value'], s_width, s_height)
            
            shop_cards.append(card)
        
        # Draw everything
        bg.draw(screen)
        
        # Draw fixed UI elements
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        money_text = money_font.render(f"Pieniądze: {profile_data['money']} $", True, (255, 255, 0))
        screen.blit(money_text, (s_width - money_text.get_width() - 50, 50))
        cars_tab.draw(screen)
        parts_tab.draw(screen)

        # Draw category tabs only when parts tab is active
        if current_tab == "parts":
            for cat_tab in category_tabs.values():
                cat_tab.draw(screen)

            # Draw filter and sort buttons
            filter_button.draw(screen)
            sort_button.draw(screen)

        back_button.draw(screen)
        
        # Draw shop cards
        for card in shop_cards:
            card.draw(screen)
        
        # Draw dialogs if active
        if confirm_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            confirm_dialog.draw(screen)
        
        pygame.display.update()
